// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAnalytics, isSupported } from "firebase/analytics";
import { getFirestore, connectFirestoreEmulator } from "firebase/firestore";
// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Check if we're in development mode
const isDevelopment = import.meta.env.DEV;

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyA6pjVLmkUImOkV7LN9IyVFmRs1ZGWlasM",
  authDomain: "lztranscription-com.firebaseapp.com",
  projectId: "lztranscription-com",
  storageBucket: "lztranscription-com.firebasestorage.app",
  messagingSenderId: "532785093644",
  appId: "1:532785093644:web:5258aa52adce43a695e229",
  measurementId: "G-9WR8P9W5HB"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firestore
const db = getFirestore(app);

// Connect to Firestore emulator in development mode
if (isDevelopment) {
  connectFirestoreEmulator(db, '127.0.0.1', 8080);
  console.log('[DEV MODE] Connected to Firestore emulator');
}

// Initialize Analytics with a check for browser support
// Using any type to avoid TypeScript errors when importing in other files
let analytics: any = null;

// Only initialize analytics in production mode
if (!isDevelopment) {
  // Check if analytics is supported before initializing
  isSupported().then(supported => {
    if (supported) {
      analytics = getAnalytics(app);
    } 
  }).catch(() => {
    // to do: add error logging and reporting
  });
} else {
  console.log('[DEV MODE] Google Analytics initialization skipped');
}

export { app, analytics, db };

