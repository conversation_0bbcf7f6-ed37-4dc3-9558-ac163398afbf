import React from 'react';
import styled from 'styled-components';
import { Section, Container } from '../../../layout/components';
import FeatureCard from './FeatureCard';

const StyledSection = styled(Section)`
  margin-top: -1rem;
  padding-bottom: ${({ theme }) => theme.spacing.xxl};
  background-color: ${({ theme }) => theme.colors.gray.light};
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    margin-top: 0;
  }
`;

const FeatureContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 3rem;
  justify-content: center;
  margin: 0 auto;
  width: fit-content;
`;

const CardWrapper = styled.div`
  display: flex;
  justify-content: center;
`;


const FeatureCardSection: React.FC = () => {
  return (
    <StyledSection>
      <Container>
        <FeatureContainer>
          <CardWrapper>
            <FeatureCard
              icon="mdi_legal.svg"
              title="Professionals with Legal Expertise"
              description="Lorem ipsum dolor sit amet consectetur."
            />
          </CardWrapper>
          <CardWrapper>
            <FeatureCard
              icon="mdi_bullseye-arrow.svg"
              title="Accurate Services"
              description="Lorem ipsum dolor sit amet consectetur."
            />
          </CardWrapper>
          <CardWrapper>
            <FeatureCard
              icon="mdi_clock-fast.svg"
              title="Dependable Turnaround"
              description="Lorem ipsum dolor sit amet consectetur."
            />
          </CardWrapper>
        </FeatureContainer>
      </Container>
    </StyledSection>
  );
};

export default FeatureCardSection;




