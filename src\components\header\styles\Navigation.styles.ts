import styled from 'styled-components';
import { NavLink } from 'react-router-dom';

export const Navigation = styled.nav`
  display: flex;
  flex: 1;
  justify-content: right;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xl};

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    display: none;
  }
`;

export const NavItem = styled(NavLink)`
  color: ${({ theme }) => theme.colors.greenDark};
  text-decoration: none;
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  transition: all ${({ theme }) => theme.transitions.default};
  padding: 0.5rem 0.25rem;
  border-radius: ${({ theme }) => theme.borderRadius.small};
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: ${({ theme }) => theme.colors.greenMedium};
    transform: scaleX(0);
    transition: transform ${({ theme }) => theme.transitions.default};
  }

  &.active {
    color: ${({ theme }) => theme.colors.greenMedium};
    
    &::after {
      transform: scaleX(1);
    }
  }

  &:hover {
    color: ${({ theme }) => theme.colors.greenMedium};
    
    &::after {
      transform: scaleX(1);
    }
  }
`;

export const OrderButton = styled(NavLink)`
  background: ${({ theme }) => theme.colors.greenLight};
  color: ${({ theme }) => theme.colors.greenDark};
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: ${({ theme }) => theme.borderRadius.small};
  font-size: ${({ theme }) => theme.fontSizes.medium};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  cursor: pointer;
  text-decoration: none;
  transition: background ${({ theme }) => theme.transitions.default};
  box-shadow: ${({ theme }) => theme.shadows.button};
  margin-left: ${({ theme }) => theme.spacing.lg};

  &:hover, &:focus {
    background: ${({ theme }) => theme.colors.greenMedium};
    color: ${({ theme }) => theme.colors.white};
    outline: none;
  }
`;


