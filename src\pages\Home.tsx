import React from 'react';
import { <PERSON> } from '../components/byPage/Main/hero';
import { GradientDivider } from '../components/ui';
import { FAQ, GetStartedSteps, ServicesOffered } from '../components';
import styled from 'styled-components';

const HomeContainer = styled.div`
  max-width: 1375px;
  margin: 0 auto;
`;

const Home: React.FC = () => (
  <HomeContainer>
    <Hero />
    <GradientDivider />
    <ServicesOffered />
    <GradientDivider />
    <GetStartedSteps />
    <GradientDivider />
    <FAQ />
  </HomeContainer>
);

export default Home;
