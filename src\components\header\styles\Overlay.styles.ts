import styled from 'styled-components';

interface OverlayProps {
  isVisible: boolean;
}

// Use shouldForwardProp to prevent isVisible from being passed to the DOM
export const Overlay = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== 'isVisible',
})<OverlayProps>`
  display: none; // Hidden by default

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    display: block; // Always display but control visibility with opacity
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 50;
    transition: opacity 0.3s ease-in-out;
    opacity: ${({ isVisible }) => (isVisible ? 1 : 0)};
    pointer-events: ${({ isVisible }) => (isVisible ? 'auto' : 'none')};
  }
`;

