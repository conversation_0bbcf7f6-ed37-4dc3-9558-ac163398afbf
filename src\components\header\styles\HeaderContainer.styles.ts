import styled from 'styled-components';

export const HeaderContainer = styled.header`
  background: ${({ theme }) => theme.colors.white};
  color: ${({ theme }) => theme.colors.greenDark};
  box-shadow: ${({ theme }) => theme.shadows.small};
  position: sticky;
  top: 0;
  z-index: 40;
  width: 100%;
`;

export const HeaderInner = styled.div`
  max-width: ${({ theme }) => theme.maxWidth};
  margin: 0 auto;
  padding: 0.5rem 2rem;
  display: flex;
  align-items: center;
  width: 100%;


  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    padding: 0.5rem 1.5rem 0.5rem 1rem;;
  }
`;
