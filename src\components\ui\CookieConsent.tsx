import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Button } from './Button';
import { setAnalyticsEnabled, isAnalyticsInDevMode } from '../../utils/analytics';

const ConsentContainer = styled.div`
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: ${({ theme }) => theme.colors.white};
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 1rem;

  @media (min-width: 768px) {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
`;

const ConsentText = styled.p`
  margin: 0;
  font-size: 0.9rem;
`;

const DevModeIndicator = styled.div`
  background-color: #ff5722;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: bold;
  margin-left: 0.5rem;
`;

const ButtonContainer = styled.div`
  display: flex;
  gap: 0.5rem;
  align-items: center;
`;

export const CookieConsent: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Check if user has already made a choice
    const hasConsent = localStorage.getItem('analytics-consent');

    if (hasConsent === null) {
      // If no choice has been made, show the consent banner
      setIsVisible(true);
    } else {
      // Apply the saved preference
      setAnalyticsEnabled(hasConsent === 'true');

      // Update gtag consent if available
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('consent', 'update', {
          'analytics_storage': hasConsent === 'true' ? 'granted' : 'denied'
        });
      }
    }
  }, []);

  const handleAccept = () => {
    localStorage.setItem('analytics-consent', 'true');
    setAnalyticsEnabled(true);
    setIsVisible(false);

    // Update gtag consent if available
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('consent', 'update', {
        'analytics_storage': 'granted'
      });
    }
  };

  const handleDecline = () => {
    localStorage.setItem('analytics-consent', 'false');
    setAnalyticsEnabled(false);
    setIsVisible(false);

    // Update gtag consent if available
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('consent', 'update', {
        'analytics_storage': 'denied'
      });
    }
  };

  if (!isVisible) {
    return null;
  }

  // Get development mode status
  const isDevMode = isAnalyticsInDevMode();

  return (
    <ConsentContainer>
      <ConsentText>
        We use cookies and analytics to improve your experience on our site.
        By clicking "Accept", you consent to the use of analytics cookies.
        {isDevMode && <span style={{ color: '#ff5722', fontWeight: 'bold' }}> (Dev Mode - No actual tracking)</span>}
      </ConsentText>
      <ButtonContainer>
        <Button variant="secondary" onClick={handleDecline}>
          Decline
        </Button>
        <Button variant="primary" onClick={handleAccept}>
          Accept
        </Button>
        {isDevMode && <DevModeIndicator>DEV MODE</DevModeIndicator>}
      </ButtonContainer>
    </ConsentContainer>
  );
};

export default CookieConsent;
