import React, { ReactNode, useEffect } from 'react';
import { usePageTracking } from './hooks/usePageTracking';
import { setAnalyticsEnabled, isAnalyticsInDevMode } from './utils/analytics';
import CookieConsent from './components/ui/CookieConsent';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';

interface AppContentProps {
  children: ReactNode;
}

// Development mode indicator that appears at the top of the page
const DevModeIndicator = styled.div`
  position: fixed;
  top: 0;
  right: 0;
  background-color: #ff5722;
  color: white;
  padding: 4px 8px;
  font-size: 12px;
  z-index: 9999;
  border-bottom-left-radius: 4px;
  opacity: 0.8;
  cursor: pointer;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 1;
  }
`;

export const AppContent: React.FC<AppContentProps> = ({ children }) => {
  // Use the page tracking hook to track page views
  usePageTracking();

  // Use navigate for routing
  const navigate = useNavigate();

  // Check if we're in development mode
  const isDevMode = isAnalyticsInDevMode();

  // Initialize analytics based on stored consent when the component mounts
  useEffect(() => {
    const hasConsent = localStorage.getItem('analytics-consent') === 'true';
    setAnalyticsEnabled(hasConsent);

    // Log development mode status
    if (isDevMode) {
      console.log('[DEV MODE] Analytics tracking is disabled in development mode');
    }
  }, [isDevMode]);

  // Handle click on dev mode indicator
  const handleDevModeClick = () => {
    navigate('/dev-settings');
  };

  return (
    <>
      {children}
      {isDevMode && (
        <DevModeIndicator onClick={handleDevModeClick} title="Click to open developer settings">
          DEV MODE
        </DevModeIndicator>
      )}
      <CookieConsent />
    </>
  );
};
