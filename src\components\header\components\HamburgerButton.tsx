import React from 'react';
import { MobileMenuToggle, HamburgerBar } from '../styles/HamburgerButton.styles';

interface HamburgerButtonProps {
  isOpen: boolean;
  onClick: () => void;
}

const HamburgerButton: React.FC<HamburgerButtonProps> = ({ isOpen, onClick }) => (
  <MobileMenuToggle
    onClick={onClick}
    aria-label="Toggle menu"
    className="mobile-toggle"
  >
    <HamburgerBar isOpen={isOpen} position="top" />
    <HamburgerBar isOpen={isOpen} position="middle" />
    <HamburgerBar isOpen={isOpen} position="bottom" />
  </MobileMenuToggle>
);

export default HamburgerButton;
