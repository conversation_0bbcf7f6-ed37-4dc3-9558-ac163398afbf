import styled from 'styled-components';
import { NavItem, OrderButton } from './Navigation.styles';

interface MobileMenuProps {
  isOpen: boolean;
}

export const MobileMenu = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== 'isOpen',
})<MobileMenuProps>`
  display: none;

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    display: flex; /* Always flex, but may be transformed off-screen */
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    width: 80%; /* Take up 80% of the screen width */
    max-width: 400px; /* But not more than 400px */
    background: ${({ theme }) => theme.colors.white};
    flex-direction: column;
    padding: ${({ theme }) => theme.spacing.lg};
    box-shadow: ${({ theme }) => theme.shadows.medium};
    z-index: 100; /* Higher than the overlay (50) */
    transform: translateX(${({ isOpen }) => (isOpen ? '0' : '100%')});
    transition: transform 0.3s ease-in-out;

    .mobile-nav-content {
      display: flex;
      flex-direction: column;
      flex: 1;
      margin-top: ${({ theme }) => theme.spacing.md};
      overflow-y: auto; /* Allow scrolling if many menu items */
    }
  }
`;

export const CloseButton = styled.button`
  display: none;

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    display: block;
    background: none;
    border: none;
    align-self: flex-end;
    font-size: ${({ theme }) => theme.fontSizes.large};
    color: ${({ theme }) => theme.colors.greenDark};
    cursor: pointer;
    margin-bottom: ${({ theme }) => theme.spacing.sm};
    padding: ${({ theme }) => theme.spacing.sm};
    position: relative;
    z-index: 10;
  }
`;

export const MobileNavItem = styled(NavItem)`
  border-radius: 0;
  padding: ${({ theme }) => `${theme.spacing.md} 0`};
  border-bottom: 1px solid ${({ theme }) => theme.colors.gray.light};
  width: 100%;
  text-align: center;

  &.active {
    background-color: ${({ theme }) => theme.colors.gray.light};
    border-radius: ${({ theme }) => theme.borderRadius.small};
    padding: ${({ theme }) => `${theme.spacing.md} ${theme.spacing.md}`};
    width: 100%;
  }

  &:last-child {
    border-bottom: none;
  }
`;

export const MobileOrderButton = styled(OrderButton)`
  margin: ${({ theme }) => theme.spacing.md} 0 0;
  width: 100%;
  display: flex;
  justify-content: center;
`;

