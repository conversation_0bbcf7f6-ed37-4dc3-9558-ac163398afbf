import React, { ReactNode } from 'react';
import styled from 'styled-components';
import { Card } from '../../../ui';
import { Heading3, Paragraph } from '../../../typography';

interface FeatureCardProps {
  icon: ReactNode;
  title: string;
  description: string;
}

const StyledFeatureCard = styled(Card)`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: ${({ theme }) => theme.spacing.xl};
  min-width: 300px;
  max-width: 300px;  
  box-shadow: 5px 5px 5px ${({ theme }) => theme.opaqueColors.grayMedium[90]};

  @media (max-width: ${({ theme }) => theme.breakpoints.at400}) {
    min-width: 200px;
    max-width: 100%;  
    padding: ${({ theme }) => theme.spacing.xl} ${({ theme }) => theme.spacing.md};
  }
`;

const IconWrapper = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const Title = styled(Heading3)`
  color: ${({ theme }) => theme.colors.greenDark};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const Description = styled(Paragraph)`
  color: red;
  margin-bottom: 0;
`;

const FeatureCard: React.FC<FeatureCardProps> = ({ icon, title, description }) => {
  return (
    <StyledFeatureCard>
      <IconWrapper>
        <img src={`/icons/${icon}`} alt={title} />
      </IconWrapper>
      <Title>{title}</Title>
      <Description>{description}</Description>
    </StyledFeatureCard>
  );
};

export default FeatureCard;
