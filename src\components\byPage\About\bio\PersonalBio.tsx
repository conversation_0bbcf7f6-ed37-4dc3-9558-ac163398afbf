import React from 'react';
import { Heading2, Paragraph } from '../../../typography';
import { 
  BioContainer, 
  BioImageContainer, 
  BioImage, 
  BioContent
} from './PersonalBio.styles';

export interface PersonalBioProps {
  /**
   * The alignment of the image (left or right)
   */
  alignImage?: 'left' | 'right';
  
  /**
   * The image source URL or path
   */
  image: string;
  
  /**
   * The name to display
   */
  name: string;
  
  /**
   * The biography text
   */
  bio: string;
}

const PersonalBio: React.FC<PersonalBioProps> = ({
  alignImage = 'left',
  image,
  name,
  bio
}) => {
  return (
    <BioContainer imageAlign={alignImage}>
      <BioImageContainer>
        <BioImage src={image} alt={`Photo of ${name}`} />
      </BioImageContainer>
      <BioContent>
        <Heading2>{name}</Heading2>
        <Paragraph>{bio}</Paragraph>
      </BioContent>
    </BioContainer>
  );
};

export default PersonalBio;
