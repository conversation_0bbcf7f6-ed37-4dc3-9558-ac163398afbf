import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';
import { PageContainer, Section } from '../components/layout/components';
import { Heading1, Heading2, Paragraph } from '../components/typography';
import { <PERSON><PERSON>, Card } from '../components/ui';
import { isAnalyticsInDevMode } from '../utils/analytics';

const DevSettingsContainer = styled(PageContainer)`
  max-width: 800px;
`;

const SettingsCard = styled(Card)`
  margin-bottom: ${({ theme }) => theme.spacing.xl};
`;

const ButtonContainer = styled.div`
  margin-top: ${({ theme }) => theme.spacing.lg};
`;

const StatusIndicator = styled.div<{ status: 'accepted' | 'declined' | 'not-set' }>`
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: ${({ theme }) => theme.borderRadius.small};
  font-weight: bold;
  font-size: 0.9rem;
  margin-bottom: ${({ theme }) => theme.spacing.md};

  background-color: ${({ status }) =>
    status === 'accepted' ? '#e8f5e9' :
    status === 'declined' ? '#ffebee' :
    '#f0f0f0'};

  color: ${({ status }) =>
    status === 'accepted' ? '#2e7d32' :
    status === 'declined' ? '#c62828' :
    '#666'};

  border: 1px solid ${({ status }) =>
    status === 'accepted' ? '#a5d6a7' :
    status === 'declined' ? '#ef9a9a' :
    '#ddd'};
`;

const StatusDot = styled.span<{ status: 'accepted' | 'declined' | 'not-set' }>`
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 8px;

  background-color: ${({ status }) =>
    status === 'accepted' ? '#4CAF50' :
    status === 'declined' ? '#F44336' :
    '#9E9E9E'};
`;

const DevSettings: React.FC = () => {
  const navigate = useNavigate();
  const isDevMode = isAnalyticsInDevMode();
  const [cookieStatus, setCookieStatus] = useState<'accepted' | 'declined' | 'not-set'>('not-set');

  // Redirect if not in dev mode and check cookie status
  useEffect(() => {
    if (!isDevMode) {
      navigate('/');
    }

    // Check current cookie consent status
    const consentValue = localStorage.getItem('analytics-consent');
    if (consentValue === 'true') {
      setCookieStatus('accepted');
    } else if (consentValue === 'false') {
      setCookieStatus('declined');
    } else {
      setCookieStatus('not-set');
    }
  }, [isDevMode, navigate]);

  const resetCookieSettings = () => {
    // Remove analytics consent from localStorage
    localStorage.removeItem('analytics-consent');

    // Update gtag consent if available
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('consent', 'update', {
        'analytics_storage': 'denied'
      });
    }

    // Update status
    setCookieStatus('not-set');

    alert('Cookie settings have been reset. The page will reload to apply changes.');
    window.location.reload();
  };

  const clearAllLocalStorage = () => {
    // Clear all localStorage items
    localStorage.clear();

    // Update status
    setCookieStatus('not-set');

    alert('All localStorage items have been cleared. The page will reload to apply changes.');
    window.location.reload();
  };

  if (!isDevMode) {
    return null; // Don't render anything if not in dev mode
  }

  return (
    <DevSettingsContainer>
      <Heading1>Developer Settings</Heading1>
      <Paragraph>
        This page is only available in development mode and provides tools for testing and debugging.
      </Paragraph>

      <Section>
        <SettingsCard>
          <Heading2>Cookie Settings</Heading2>

          <StatusIndicator status={cookieStatus}>
            <StatusDot status={cookieStatus} />
            {cookieStatus === 'accepted' && 'Cookies are currently ACCEPTED'}
            {cookieStatus === 'declined' && 'Cookies are currently DECLINED'}
            {cookieStatus === 'not-set' && 'Cookie preference is NOT SET'}
          </StatusIndicator>

          <Paragraph>
            Reset cookie consent to test the cookie consent banner and analytics functionality.
            This will remove the stored consent preference and reload the page.
          </Paragraph>
          <ButtonContainer>
            <Button variant="primary" onClick={resetCookieSettings}>
              Reset Cookie Settings
            </Button>
          </ButtonContainer>
        </SettingsCard>

        <SettingsCard>
          <Heading2>Local Storage</Heading2>
          <Paragraph>
            Clear all items in localStorage to reset the application state.
            This will remove all stored preferences and reload the page.
          </Paragraph>
          <ButtonContainer>
            <Button variant="secondary" onClick={clearAllLocalStorage}>
              Clear All Local Storage
            </Button>
          </ButtonContainer>
        </SettingsCard>

        <Button variant="secondary" onClick={() => navigate('/')}>
          Back to Home
        </Button>
      </Section>
    </DevSettingsContainer>
  );
};

export default DevSettings;
