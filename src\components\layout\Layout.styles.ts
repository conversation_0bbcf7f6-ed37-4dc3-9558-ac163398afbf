import styled from 'styled-components';

export const AppContainer = styled.div`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
`;

export const MainContent = styled.main`
  padding: 0rem 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
`;

export const MainContainer = styled.div`
  width: 100%;
  max-width: ${({ theme }) => theme.maxWidth};
  margin: 0 auto;
  padding: 0rem 1rem 1rem 1rem;
  background: ${({ theme }) => theme.colors.white};
  flex: 1;

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    padding: 0rem 0.5rem 1rem 0.5rem;
  }
`;

