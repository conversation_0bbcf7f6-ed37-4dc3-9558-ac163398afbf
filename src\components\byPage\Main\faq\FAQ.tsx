import React, { useState, useEffect } from 'react';
import { Heading2 } from '../../../typography';
import FAQItem from './FAQItem';
import FAQContent from './FAQContent';
import { <PERSON>QS<PERSON><PERSON>, FAQContainer, FAQList, FAQOuterContainer } from './FAQ.styles';

// FAQ data
const faqData = [
  {
    question: 'What is transcription?',
    content: {
      title: 'Transcription is the process of converting audio or video recordings into written text documents.',
    }
  },
  {
    question: 'What services does LZ Transcription provide?',
    content: {
      title: 'LZ Transcription offers professional legal transcription services tailored to the needs of individuals and law firms of all sizes:',
      listItems: [
        'Certified transcripts from all Ontario Court proceedings',
        'Police Interviews, 911 calls, recorded voicemail messages',
        'Transcription of discoveries, statements made by involved parties, and other legal pleadings'
      ],
      contactText: 'If you require a service not listed above, please don\'t hesitate to contact us. We would be happy to discuss how we can accommodate your specific needs.'
    }
  },
  {
    question: 'How do you guarantee accuracy in transcribed documents?',
    content: {
      title: 'We ensure accuracy through our rigorous quality control process:',
      listItems: [
        'All transcriptions are performed by professionals with legal backgrounds',
        'Every document undergoes a secondary review by another authorized court transcriptionist',
        'Our team stays updated on legal terminology and procedures'
      ]
    }
  },
  {
    question: 'How long does it take to have an audio file transcribed?',
    content: {
      title: 'Turnaround time depends on the length and complexity of the audio.',
      listItems: [
        
      ],
      contactText: 'Please contact us for a specific timeline based on your project requirements.'
    }
  },
  {
    question: 'What is the pricing for transcribing legal documents?',
    content: {
      title: 'Fees for Ontario Court transcripts are set out in Ontario Regulation 94/14 (HST not included).',
      listItems: [
        
      ],
      contactText: 'For all other transcription matters, please contact us for pricing. We would be happy to discuss this is more detail.'
    }
  }
];

const FAQ: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState(1); // Default to the second item (services)
  const [isMobile, setIsMobile] = useState(false);

  // Check if we're on mobile/tablet screen
  useEffect(() => {
    const checkIfMobile = () => {
      // Get the tablet breakpoint from theme
      const tabletBreakpoint = 768; // This should match theme.breakpoints.tablet
      setIsMobile(window.innerWidth <= tabletBreakpoint);
    };

    // Initial check
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  const handleItemClick = (index: number) => {
    // If clicking the already active item in mobile view, close it
    if (isMobile && index === activeIndex) {
      setActiveIndex(-1); // Set to an invalid index to close all
    } else {
      // Otherwise, set the new active index for both mobile and desktop
      setActiveIndex(index);
    }
  };

  return (
    <FAQSection>
      <FAQOuterContainer>
        <Heading2>Frequently Asked Questions</Heading2>
        <FAQContainer>
          <FAQList>
            {faqData.map((faq, index) => (
              <FAQItem
                key={index}
                question={faq.question}
                isActive={index === activeIndex}
                onClick={() => handleItemClick(index)}
                content={faq.content}
                isMobileExpanded={isMobile && index === activeIndex}
              />
            ))}
          </FAQList>
          {activeIndex >= 0 && <FAQContent content={faqData[activeIndex].content} />}
        </FAQContainer>
      </FAQOuterContainer>
    </FAQSection>
  );
};

export default FAQ;
