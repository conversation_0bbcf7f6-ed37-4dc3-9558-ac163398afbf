import React from 'react';
import {
  MobileMenu,
  CloseButton,
  MobileNavItem,
  MobileOrderButton
} from '../styles/MobileNavigation.styles';
import { NavigationItem } from '../Header';

interface MobileNavigationProps {
  navigationItems: NavigationItem[];
  isOpen: boolean;
  onClose: () => void;
}

const MobileNavigation: React.FC<MobileNavigationProps> = ({ navigationItems, isOpen, onClose }) => (
  <MobileMenu isOpen={isOpen} className="mobile-menu" aria-hidden={!isOpen}>
    <CloseButton onClick={onClose} aria-label="Close menu">✕</CloseButton>
    <div className="mobile-nav-content">
      <MobileNavItem
            key="/"
            to="/"
            onClick={onClose}
          >
            Home
          </MobileNavItem>
      {navigationItems.map((item) => (
        item.isButton ? (
          <MobileOrderButton key={item.path} to={item.path} onClick={onClose}>
            {item.label}
          </MobileOrderButton>
        ) : (
          <MobileNavItem
            key={item.path}
            to={item.path}
            end={item.exact}
            onClick={onClose}
          >
            {item.label}
          </MobileNavItem>
        )
      ))}
    </div>
  </MobileMenu>
);

export default MobileNavigation;
