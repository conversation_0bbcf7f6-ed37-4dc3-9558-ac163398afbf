import React from 'react';
import { HeroContent as StyledHeroContent, HeroActions, HeroButton, HeroHeading } from './Hero.styles';
import { LargeText } from '../../../typography';
import { useNavigate } from 'react-router-dom';
import { logButtonClick } from '../../../../utils/analytics';

const HeroContent: React.FC = () => {
  const navigate = useNavigate();

  const handleLearnMoreClick = () => {
    // Track the button click event
    logButtonClick('learn_more', 'hero_section');
    navigate('/services');
  };

  const handleContactUsClick = () => {
    // Track the button click event
    logButtonClick('contact_us', 'hero_section');
    navigate('/contact');
  };

  return (
    <StyledHeroContent>
      <HeroHeading>
        Precise, Timely & Trustworthy Transcription Services for All Legal Matters
      </HeroHeading>
      <LargeText style={{ marginBottom: '2rem', letterSpacing: '0.025em', fontWeight: 'bold' }}>

      </LargeText>
      <HeroActions>
        <HeroButton variant="secondary" onClick={handleLearnMoreClick}>
          Learn More
        </HeroButton>
        <HeroButton variant="primary" onClick={handleContactUsClick}>
          Contact Us
        </HeroButton>
      </HeroActions>
    </StyledHeroContent>
  );
};

export default HeroContent;
