import React, { ReactNode } from 'react';
import Header from '../header/Header';
import Footer from '../footer/Footer';
import { AppContainer, MainContent, MainContainer } from './Layout.styles';

interface LayoutProps {
  children: ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <AppContainer>
      <Header />
      <MainContent>
        <MainContainer>
          {children}
        </MainContainer>
      </MainContent>
      <Footer />
    </AppContainer>
  );
};

export default Layout;
