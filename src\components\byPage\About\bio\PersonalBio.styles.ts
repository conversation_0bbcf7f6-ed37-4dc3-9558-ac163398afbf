import styled from 'styled-components';

// Use shouldForwardProp to prevent imageAlign from being passed to the DOM
export const BioContainer = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== 'imageAlign',
})<{ imageAlign: 'left' | 'right' }>`
  display: flex;
  flex-direction: ${({ imageAlign }) => imageAlign === 'left' ? 'row' : 'row-reverse'};
  gap: ${({ theme }) => theme.spacing.xl};
  margin: ${({ theme }) => theme.spacing.xl} 0;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
`;

export const BioImageContainer = styled.div`
  flex: 0 0 auto;
  width: 340px;
  height: 340px;
  border-radius: ${({ theme }) => theme.borderRadius.xlarge};
  overflow: hidden;
  box-shadow: ${({ theme }) => theme.shadows.card};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    width: 250px;
    height: 250px;
  }
`;

export const BioImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

export const BioContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
`;
