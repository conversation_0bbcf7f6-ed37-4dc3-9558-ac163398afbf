import React from 'react';
import styled from 'styled-components';
import { Section, Container } from '../../../layout/components';
import { Heading2, Paragraph } from '../../../typography';

// Styled components for the GetStartedSteps
const GetStartedSection = styled(Section)`
  padding: ${({ theme }) => theme.spacing.xl} 0;
  background-color: ${({ theme }) => theme.colors.gray.light};
  border-radius: ${({ theme }) => theme.borderRadius.medium};
  margin: 1rem 0 ${({ theme }) => theme.spacing.xxl};
`;

const StepsContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
`;

const StepsWrapper = styled.div`
  display: flex;
  width: 100%;
  margin-bottom: ${({ theme }) => theme.spacing.xl};
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xl};
`;

const StepItem = styled.div`
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  width: 100%;
  position: relative;
`;

const StepIconContainer = styled.div`
  background: ${({ theme }) => theme.colors.white};
  border-radius: ${({ theme }) => theme.borderRadius.large};
  width: 150px;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: ${({ theme }) => theme.shadows.card};
  padding: ${({ theme }) => theme.spacing.lg};
  margin-right: ${({ theme }) => theme.spacing.xl};
  flex-shrink: 0;

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    width: 120px;
    height: 120px;
  }
`;

const StepIcon = styled.img`
  width: auto;
  height: 80px;
`;

const StepNumber = styled.div`
  background: ${({ theme }) => theme.colors.greenLight};
  color: ${({ theme }) => theme.colors.white};
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  font-size: ${({ theme }) => theme.fontSizes.xlarge};
  position: absolute;
  top: -10px;
  left: -10px;
`;

const StepDescription = styled(Paragraph)`
  font-size: ${({ theme }) => theme.fontSizes.large};
  line-height: 1.4;
  flex: 1;
  text-align: left;
  min-height: 140px;
  display: flex;
  align-items: center;
  justify-content: left; 

  padding: ${({ theme }) => theme.spacing.sm};

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    font-size: ${({ theme }) => theme.fontSizes.medium};
    align-items: flex-start; /* Top align text */
    min-height: 100px;
  }
`;

const HeadingContainer = styled.div`
  text-align: center;
  margin: 0 0 1.5rem 0;
`;


// Step data
const steps = [
  {
    icon: '/icons/Get_Started_1.svg',
    number: '1',
    height: '110px',
    description: 'Contact us and tell us what your are looking for'
  },
  {
    icon: '/icons/Get_Started_2.svg',
    number: '2',
    height: '110px',
    description: 'Transcriptionist acquires recording and discussed with you the estimated cost'
  },
  {
    icon: '/icons/Get_Started_3.svg',
    number: '3',
    height: '110px',
    description: 'Our authorized transcription professionals will translate your audio document into text'
  },
  {
    icon: '/icons/Get_Started_4.svg',
    number: '4',
    height: '110px',
    description: 'Transcribed documents will be reviewed by a second authorized transcriptionist to ensure accuracy'
  },
  {
    icon: '/icons/Get_Started_5.svg',
    number: '✓',
    height: '110px',
    description: 'Completed transcribed document will be sent to you.'
  }
];

const GetStartedSteps: React.FC = () => {
  return (
    <>
      <GetStartedSection>
        <HeadingContainer>
          <Heading2>How to get started</Heading2>
        </HeadingContainer>
        <Container>
          <StepsContainer>
            <StepsWrapper>
              {steps.map((step, index) => (
                <React.Fragment key={index}>
                  <StepItem>
                    <StepIconContainer>
                      <StepIcon src={step.icon} alt={`Step ${step.number}`} style={{ height: step.height }} />
                    </StepIconContainer>
                    <StepNumber>{step.number}</StepNumber>
                    <StepDescription>{step.description}</StepDescription>
                  </StepItem>
                </React.Fragment>
              ))}
            </StepsWrapper>
          </StepsContainer>
        </Container>
      </GetStartedSection>
    </>
  );
};

export default GetStartedSteps;
