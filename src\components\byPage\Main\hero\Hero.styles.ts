import styled from 'styled-components';
import { Button } from '../../../ui';
import { Heading2 } from '../../../typography';

export const HeroSection = styled.section`
  position: relative;
  min-height: 500px;
  margin: 0rem 0 2rem 0;
  padding-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
    
  background-color: ${({ theme }) => theme.opaqueColors.greenLight[5]};

    // &::after {
    //   content: '';
    //   position: absolute;
    //   bottom: 0;
    //   left: 0;
    //   width: 100%;
    //   height: 25px;
    //   background: ${({ theme }) => `linear-gradient(to right, ${theme.colors.greenDark}, ${theme.colors.greenMedium}, ${theme.colors.greenLight})`};
    // }

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    min-height: 500px;
    flex-direction: column;
  }
`;


export const HeroHeading = styled(Heading2)`
  line-height: 3rem;

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    text-align: center;
  }
`;

export const HeroContent = styled.div`
  flex: 0 0 45%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  text-align: left;
  padding: ${({ theme }) => theme.spacing.sm};
  border-radius: ${({ theme }) => theme.borderRadius.medium};
  margin-left: ${({ theme }) => theme.spacing.xl};

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    flex: 1;
    margin: ${({ theme }) => theme.spacing.xl} auto;
    padding: ${({ theme }) => theme.spacing.sm};
    width: 90%;
  }
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    align-items: center;
  }
`;

export const HeroButton = styled(Button)`

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    width: 18rem;
  }
`;

export const HeroActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.lg};

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    flex-direction: column;
    justify-content: center;
    gap: ${({ theme }) => theme.spacing.sm};
  }
`;

export const HeroImageContainer = styled.div`
  flex: 0 0 45%;
  display: flex;
  justify-content: center;
  margin: 0 1rem 0 0;
  align-items: center;
  padding: ${({ theme }) => theme.spacing.lg};

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    display: none;
  }
`;

export const HeroImg = styled.img`
  max-width: 100%;
  height: auto;
  margin: 0 1rem 0 1rem;
  border-radius: ${({ theme }) => theme.borderRadius.large};
`;

