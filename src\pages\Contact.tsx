import React, { useState, useEffect } from 'react';
import { Heading1, Heading3 } from '../components/typography';
import { FormGroup, Input, Textarea, Label } from '../components/form';
import { Button } from '../components/ui';
import {
  ContactContainer,
  ContactInfoContainer,
  ContactFormContainer,
  FormCard,
  ContactInfoText,
  ContactItemContainer,
  ContactItem,
  FormRow,
  FormColumn,
  FormConfirmationContainer
} from './Contact.styles';
import { PageContainer } from '../components';
import styled from 'styled-components';
import { logFormSubmit } from '../utils/analytics';
import { collection, addDoc } from 'firebase/firestore';
import { db } from '../firebase';

const ErrorMessage = styled.div`
  color: #d32f2f;
  font-size: 0.875rem;
  font-family: ${({ theme }) => theme.fonts.primary};
  margin-top: 0.25rem;
`;

const Contact: React.FC = () => {
  const initialFormState = {
    firstName: { content: '', madeContact: false },
    lastName: { content: '', madeContact: false },
    email: { content: '', madeContact: false },
    phone: { content: '', madeContact: false },
    message: { content: '', madeContact: false }
  };
  const [form, setForm] = useState(initialFormState);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [submitted, setSubmitted] = useState(false);

  const validateForm = (isSubmit: boolean = false): { [key: string]: string } => {
    const newErrors: { [key: string]: string } = {};

    // Validate first name
    if ((isSubmit || form.firstName.madeContact) && !form.firstName.content.trim()) {
      newErrors.firstName = 'First name is required';
    }

    // Validate last name
    if ((isSubmit || form.lastName.madeContact) && !form.lastName.content.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    // Validate email
    if (isSubmit || form.email.madeContact)
      if (!form.email.content.trim()) {
        newErrors.email = 'Email is required';
      } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(form.email.content)) {
        newErrors.email = 'Please enter a valid email address';
      }

    // Validate phone number
    if (isSubmit || form.phone.madeContact)
      if (!form.phone.content.trim()) {
        newErrors.phone = 'Phone number is required';
      } else {
        // Comprehensive phone validation regex
        // Accepts formats: (*************, ************, ************, 1234567890, +1 ************
        const phoneRegex = /^(\+?\d{1,2}\s?)?(\(\d{3}\)|\d{3})[-.\s]?\d{3}[-.\s]?\d{4}$/;
        if (!phoneRegex.test(form.phone.content)) {
          newErrors.phone = 'Please enter a valid phone number (e.g., ************)';
        }
      }

    // Validate message
    if ((isSubmit || form.message.madeContact) && !form.message.content.trim()) {
      newErrors.message = 'Message is required';
    }

    return newErrors;
  };

  useEffect(() => {
    setErrors(validateForm(false));
  }, [form]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setForm({ ...form, [name]: { content: value, madeContact: true } });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate all fields
    const validationErrors = validateForm(true);
    setErrors(validationErrors);

    // If there are errors, prevent form submission
    if (Object.keys(validationErrors).length > 0) {
      logFormSubmit('contact_form', false);
      return;
    }

    try {
      // Save to Firestore to trigger the Cloud Function
      await addDoc(collection(db, "contactSubmissions"), {
        firstName: form.firstName.content,
        lastName: form.lastName.content,
        email: form.email.content,
        phone: form.phone.content,
        message: form.message.content,
        timestamp: new Date()
      });
      
      // If successful, show success message
      setSubmitted(true);
      logFormSubmit('contact_form', true);
    } catch (error) {
      console.error("Error submitting form:", error);
      // Handle error (show error message to user)
    }
  };

  return (
    <PageContainer>

      <ContactContainer>

        {submitted ? (
          <FormConfirmationContainer>
            <Heading3 style={{ textAlign: 'center' }}>
              Thank you for your message! We'll get back to you soon.
            </Heading3>
            <br />
            <Button onClick={() => { setSubmitted(false); setForm(initialFormState); }}>Back to Contact Us</Button>
          </FormConfirmationContainer>
        ) : (
          <>
        <ContactInfoContainer>
          <Heading1>Contact Us</Heading1>
          <ContactInfoText>
            If you have any questions about our services, or a project, simply fill out the form and a
            representative we'll be in touch with you shortly.
          </ContactInfoText>
          <ContactItemContainer>
            <ContactItem>
              <img src="/icons/location-unfilled.svg" alt="Location" />
              Ontario, Canada
            </ContactItem>
            <ContactItem>
              <img src="/icons/phone-unfilled.svg" alt="Phone" />
              (*************
            </ContactItem>
            <ContactItem>
              <img src="/icons/email-unfilled.svg" alt="Email" />
              <a href="mailto:<EMAIL>"><EMAIL></a>
            </ContactItem>
          </ContactItemContainer>
        </ContactInfoContainer>
          <ContactFormContainer>
            <FormCard>
              <form onSubmit={handleSubmit}>
                <FormRow>
                  <FormColumn>
                    <FormGroup>
                      <Label htmlFor="firstName">First Name*</Label>
                      <Input
                        type="text"
                        id="firstName"
                        name="firstName"
                        value={form.firstName.content}
                        onChange={handleChange}
                        placeholder="Enter your first name"
                        style={{ borderColor: errors.firstName ? '#d32f2f' : undefined }}
                      />
                      {errors.firstName && <ErrorMessage>{errors.firstName}</ErrorMessage>}
                    </FormGroup>
                  </FormColumn>
                  <FormColumn>
                    <FormGroup>
                      <Label htmlFor="lastName">Last Name*</Label>
                      <Input
                        type="text"
                        id="lastName"
                        name="lastName"
                        value={form.lastName.content}
                        onChange={handleChange}
                        placeholder="Enter your last name"
                        style={{ borderColor: errors.lastName ? '#d32f2f' : undefined }}
                      />
                      {errors.lastName && <ErrorMessage>{errors.lastName}</ErrorMessage>}
                    </FormGroup>
                  </FormColumn>
                </FormRow>

                <FormGroup>
                  <Label htmlFor="email">Email*</Label>
                  <Input
                    type="email"
                    id="email"
                    name="email"
                    value={form.email.content}
                    onChange={handleChange}
                    placeholder="Enter your email address"
                    style={{ borderColor: errors.email ? '#d32f2f' : undefined }}
                  />
                  {errors.email && <ErrorMessage>{errors.email}</ErrorMessage>}
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="phone">Phone Number*</Label>
                  <Input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={form.phone.content}
                    onChange={handleChange}
                    placeholder="e.g., ************"
                    title="Phone number format: ************"
                    style={{ borderColor: errors.phone ? '#d32f2f' : undefined }}
                  />
                  {errors.phone && <ErrorMessage>{errors.phone}</ErrorMessage>}
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="message">Message*</Label>
                  <Textarea
                    id="message"
                    name="message"
                    value={form.message.content}
                    onChange={handleChange}
                    placeholder="Enter your message here"
                    rows={5}
                    style={{ borderColor: errors.message ? '#d32f2f' : undefined }}
                  />
                  {errors.message && <ErrorMessage>{errors.message}</ErrorMessage>}
                </FormGroup>

                <Button variant="primary" type="submit" fullWidth>Submit Form</Button>
              </form>
            </FormCard>
          </ContactFormContainer>
          </>
        )}
      </ContactContainer>
    </PageContainer>
  );
};

export default Contact;


