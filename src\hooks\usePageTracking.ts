import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { logPageView } from '../utils/analytics';

/**
 * Custom hook to track page views in Google Analytics
 * This hook automatically logs page views when the route changes
 */
export const usePageTracking = () => {
  const location = useLocation();
  
  useEffect(() => {
    // Get the page title from the document or use a default
    const pageTitle = document.title || 'LZ Transcription';
    
    // Log the page view with the current path
    logPageView(location.pathname, pageTitle);
    
    // You can also update the document title based on the route if needed
    // document.title = `${getPageTitle(location.pathname)} | LZ Transcription`;
  }, [location]);
};

export default usePageTracking;
