import React from 'react';
import { Overlay as StyledOverlay } from '../styles/Overlay.styles';

interface OverlayProps {
  isVisible: boolean;
  onClick: () => void;
}

const Overlay: React.FC<OverlayProps> = ({ isVisible, onClick }) => {
  // Stop propagation to prevent clicks from reaching elements underneath
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onClick();
  };

  return <StyledOverlay isVisible={isVisible} onClick={handleClick} />;
};

export default Overlay;
