import React from 'react';
import {
  Footer<PERSON>ontainer,
  FooterGradient,
  FooterContent,
  FooterColumnLeft,
  FooterColumnRight,
  FooterHeading,
  FooterLink,
  FooterImage,
  ContactItem,
  CopyrightContainer
} from './Footer.styles';
import { SmallText } from '../typography';

const Footer: React.FC = () => (
  <FooterContainer>
    <FooterGradient />
    <FooterContent>
      <FooterColumnLeft>
        {/* <FooterHeading>Legal</FooterHeading>
        <FooterLink href="/privacy-policy" style={{ marginBottom: '15px' }}>Privacy Policy</FooterLink>
        <FooterLink href="/terms-of-use">Terms of Use</FooterLink> */}
      </FooterColumnLeft>

      <FooterColumnRight>
        <FooterHeading>Reach us</FooterHeading>
        <ContactItem>
          (*************
          <FooterImage src="/icons/bxs_phone-call.svg" alt="Phone" />
        </ContactItem>
        <ContactItem>
          <FooterLink href="mailto:<EMAIL>"><EMAIL></FooterLink>
          <FooterImage src="/icons/ic_sharp-email.svg" alt="Email" />
        </ContactItem>
        <ContactItem>
          Ontario, Canada
          <FooterImage src="/icons/location-filled.svg" alt="Location" />
        </ContactItem>
      </FooterColumnRight>
    </FooterContent>

    <CopyrightContainer>
      <SmallText style={{ color: 'white' }}>&copy; {new Date().getFullYear()} LZ Transcription. All Rights Reserved</SmallText>
    </CopyrightContainer>
  </FooterContainer>
);

export default Footer;
