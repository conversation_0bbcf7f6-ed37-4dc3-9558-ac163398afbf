import React from 'react';
import { Paragraph, ListItem } from '../../../typography';
import { FAQContentContainer, ServicesList, ContactText } from './FAQ.styles';

interface FAQContentProps {
  content: {
    title: string;
    description?: string;
    listItems?: string[];
    contactText?: string;
  };
  useContainer?: boolean;
}

const FAQContent: React.FC<FAQContentProps> = ({ content, useContainer = true }) => {
  const ContentBody = () => (
    <>
      <Paragraph>{content.title}</Paragraph>

      {content.description && <Paragraph>{content.description}</Paragraph>}

      {content.listItems && content.listItems.length > 0 && (
        <>
          <ServicesList>
            {content.listItems.map((service, index) => (
              <ListItem key={index}>{service}</ListItem>
            ))}
          </ServicesList>
        </>
      )}

      {content.contactText && (
        <ContactText>{content.contactText}</ContactText>
      )}
    </>
  );

  if (useContainer) {
    return (
      <FAQContentContainer>
        <ContentBody />
      </FAQContentContainer>
    );
  }

  return <ContentBody />;
};

export default FAQContent;
