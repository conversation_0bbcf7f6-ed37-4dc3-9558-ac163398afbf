import styled from 'styled-components';
import { Card } from '../components/ui';

export const ContactContainer = styled.div`
  display: flex;
  flex-direction: row;
  gap: ${({ theme }) => theme.spacing.xxl};
  margin: ${({ theme }) => theme.spacing.xl} 0;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    flex-direction: column;
  }
`;

export const ContactInfoContainer = styled.div`
  flex: 1;
`;

export const ContactFormContainer = styled.div`
  flex: 1.5;
`;

export const FormCard = styled(Card)`
  padding: ${({ theme }) => theme.spacing.xl};
`;

export const ContactInfoText = styled.div`
  flex: 1.5;
  font-size: ${({ theme }) => theme.fontSizes.medium};
  line-height: 1.6;
  margin-bottom: ${({ theme }) => theme.spacing.xl};
`;

export const FormConfirmationContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  width: 100%;
  padding: ${({ theme }) => theme.spacing.xl};
`;

export const ContactItemContainer = styled.div`
  margin-top: ${({ theme }) => theme.spacing.xl};
`;

export const ContactItem = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.md};
  font-size: ${({ theme }) => theme.fontSizes.medium};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  
  img {
    margin-right: ${({ theme }) => theme.spacing.sm};
    color: ${({ theme }) => theme.colors.greenMedium};
  }
`;

export const FormRow = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
  }
`;

export const FormColumn = styled.div`
  flex: 1;
`;
