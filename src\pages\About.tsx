import React from 'react';
import { Heading1, Paragraph, SmallText } from '../components/typography';
import { PageContainer, Section } from '../components/layout/components';
import LinkText from '../components/typography/Link';

const About: React.FC = () => (
  <PageContainer>
    <Heading1>About LZ Transcription</Heading1>
    <Section>
      <Paragraph>
        Founded by a former police officer (Zac) and former litigator (Lynn), we understand the critical importance of accuracy, speed, and integrity in the legal field. With firsthand experience in the legal system and appreciating the need for accurate transcriptions when you need them, we bring a unique perspective and a deep respect for the sensitive nature of legal documentation.
      </Paragraph>

      <Paragraph>
        As Court Transcriptionists authorized by the Ministry of the Attorney General to transcribe Ontario court matters, we specialize in legal transcription for anyone who may need it, including professionals, individuals, and government agencies. Every transcript is carefully reviewed and professionally formatted, with strict attention to detail and legal terminology, following the guidelines imposed by the Ministry.
      </Paragraph>

      <Paragraph>
        Whether you're a law firm, court reporter, or private client, you can count on <PERSON><PERSON> and <PERSON> to provide transcription services with integrity, speed, and professionalism.
      </Paragraph>

      <Paragraph>
        If you need a deposition, recorded interview, hearing, or other recording transcribed, please explore this website or contact us at <LinkText href="mailto:<EMAIL>"><EMAIL></LinkText> to find the answers to any questions you may have.
      </Paragraph>

      <SmallText>
        <i>* We do not provide legal advice.</i>
      </SmallText>
    </Section>
  </PageContainer>
);

export default About;
