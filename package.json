{"name": "lz-transcription", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:host": "vite --host", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "deploy": "firebase deploy --only hosting", "build:deploy": "npm run build && npm run deploy", "emulator": "firebase emulators:start"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "firebase": "^11.6.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.5.0", "styled-components": "^6.1.17"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/styled-components": "^5.1.34", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}