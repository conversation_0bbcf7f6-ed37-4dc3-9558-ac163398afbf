import React from 'react';
import { Navigation, NavItem, OrderButton } from '../styles/Navigation.styles';
import { NavigationItem } from '../Header';

interface DesktopNavigationProps {
  navigationItems: NavigationItem[];
}

const DesktopNavigation: React.FC<DesktopNavigationProps> = ({ navigationItems }) => (
  <Navigation>
    {navigationItems.map((item) => (
      item.isButton ? (
        <OrderButton key={item.path} to={item.path}>{item.label}</OrderButton>
      ) : (
        <NavItem key={item.path} to={item.path} end={item.exact}>
          {item.label}
        </NavItem>
      )
    ))}
  </Navigation>
);

export default DesktopNavigation;
