/**
 * Import function triggers from their respective submodules:
 *
 *
 * See a full list of supported triggers at https://firebase.google.com/docs/functions
 */

import * as logger from "firebase-functions/logger";
import { onDocumentCreated } from "firebase-functions/v2/firestore";
import { initializeApp } from "firebase-admin/app";
import * as nodemailer from "nodemailer";
import mailgunTransport from "nodemailer-mailgun-transport";

// Initialize without full admin SDK
initializeApp();

// Define interface for contact form data
interface ContactFormData {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    message: string;
}

// Configure email transporter for Mailgun API
const mailgunAuth = {
    auth: {
        api_key: process.env.MAILGUN_API_KEY || "your-mailgun-api-key",
        domain: process.env.MAILGUN_DOMAIN || "your-mailgun-domain"
    }
};

const transporter = nodemailer.createTransport(mailgunTransport(mailgunAuth));

// Email sending function
export const sendContactEmail = onDocumentCreated(
    "contactSubmissions/{docId}",
    async (event) => {
        // Get the data from the newly created document
        const snapshot = event.data;
        if (!snapshot) {
            logger.error("No data associated with the event");
            return { error: "No data found" };
        }

        const data = snapshot.data() as ContactFormData;

        // Email content
        const mailOptions = {
            from: "LZ Transcription <<EMAIL>>",
            to: process.env.NODE_ENV === 'production' 
                ? ["<EMAIL>", "<EMAIL>"] // Production emails
                : ["<EMAIL>"], // Development emails
            replyTo: data.email,
            subject: (process.env.NODE_ENV === 'production'  ? `` : `(LOCAL)`) 
                + `New Contact Form Submission from ${data.firstName} ${data.lastName}`,
            text: `
                    Name: ${data.firstName} ${data.lastName}
                    Email: ${data.email}
                    Phone: ${data.phone}

                    Message:
                        ${data.message}
                 `,
            html: `
                    <h2>New Contact Form Submission</h2>
                    <p><strong>Name:</strong> ${data.firstName} ${data.lastName}</p>
                    <p><strong>Email:</strong> ${data.email}</p>
                    <p><strong>Phone:</strong> ${data.phone}</p>
                    <h3>Message:</h3>
                    <p>${data.message}</p>
      `,
        };

        try {
            await transporter.sendMail(mailOptions);
            logger.info(`Email sent successfully for submission from ${data.email}`);
            return { success: true };
        } catch (error) {
            logger.error("Error sending email:", error);
            return { error: (error as Error).message };
        }
    }
);






