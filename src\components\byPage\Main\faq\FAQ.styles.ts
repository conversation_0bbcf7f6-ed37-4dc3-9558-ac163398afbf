import styled from 'styled-components';
import { Section } from '../../../layout/components';

export const FAQSection = styled(Section)`
  padding: ${({ theme }) => theme.spacing.xl} 0;
  
`;
export const FAQOuterContainer = styled.div`
 max-width: ${({ theme }) => theme.maxWidth};
  margin: 0 auto;
`;

export const FAQContainer = styled.div`
  display: flex;
  flex-direction: row;
  gap: ${({ theme }) => theme.spacing.xl};

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    flex-direction: column;
  }
`;

export const FAQList = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.sm};
`;

export const FAQContentContainer = styled.div`
  flex: 1.5;
  background-color: ${({ theme }) => theme.colors.gray.light};
  padding: ${({ theme }) => theme.spacing.xl};
  border-radius: ${({ theme }) => theme.borderRadius.medium};

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    display: none; /* Hide the desktop content container on mobile */
  }
`;

export const FAQItem = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
`;

export const FAQItemButton = styled.button.withConfig({
  shouldForwardProp: (prop) => prop !== 'isActive',
})<{ isActive: boolean }>`
  display: flex;
  align-items: center;
  width: 100%;
  text-align: left;
  padding: ${({ theme }) => theme.spacing.md};
  border: none;
  background-color: ${({ isActive, theme }) =>
    isActive ? theme.colors.greenMedium : theme.colors.gray.light};
  color: ${({ isActive, theme }) =>
    isActive ? theme.colors.white : theme.colors.greenDark};
  border-radius: ${({ theme }) => theme.borderRadius.medium};
  font-size: ${({ theme }) => theme.fontSizes.medium};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.default};

  &:hover {
    background-color: ${({ isActive, theme }) =>
      isActive ? theme.colors.greenMedium : theme.colors.gray.medium};
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    border-radius: ${({ isActive, theme }) =>
      isActive
        ? `${theme.borderRadius.medium} ${theme.borderRadius.medium} 0 0`
        : theme.borderRadius.medium};
    margin-bottom: ${({ isActive }) => isActive ? '0' : undefined};
  }
`;

export const FAQItemIcon = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== 'isActive',
})<{ isActive: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: ${({ isActive, theme }) =>
    isActive ? theme.colors.greenMedium : theme.colors.gray.light};
  margin-right: ${({ theme }) => theme.spacing.md};

  &::before {
    content: '';
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: ${({ isActive, theme }) =>
      isActive ? theme.colors.white : theme.colors.greenMedium};
  }
`;

export const FAQItemText = styled.span`
  flex: 1;
`;

export const FAQItemArrow = styled.span`
  margin-left: ${({ theme }) => theme.spacing.sm};
  font-size: ${({ theme }) => theme.fontSizes.large};
  font-family: Arial, sans-serif;
`;

export const ServicesList = styled.ul`
  list-style-type: disc;
  padding-left: ${({ theme }) => theme.spacing.xl};
  margin-top: ${({ theme }) => theme.spacing.md};
`;

export const ContactText = styled.p`
  margin-top: ${({ theme }) => theme.spacing.lg};
  font-style: italic;
`;

export const MobileAccordionContent = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== 'isOpen',
})<{ isOpen: boolean }>`
  display: none;
  overflow: hidden;
  max-height: 0;

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    display: block;
    max-height: ${({ isOpen }) => (isOpen ? '1000px' : '0')};
    opacity: ${({ isOpen }) => (isOpen ? '1' : '0')};
    background-color: ${({ theme }) => theme.colors.gray.light};
    padding: ${({ isOpen, theme }) => isOpen ? theme.spacing.lg : '0'};
    border-radius: 0 0 ${({ theme }) => theme.borderRadius.medium} ${({ theme }) => theme.borderRadius.medium};
    margin-bottom: ${({ theme }) => theme.spacing.sm};
    transition: all ${({ theme }) => theme.transitions.slow};
  }
`;

export const FAQItemWrapper = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.sm};
`;
