import React from 'react';
import { Heading1, Heading2, Paragraph, SmallText } from '../components/typography';
import styled from 'styled-components';
import { PageContainer, Section } from '../components';

// Styled components for tables
const TableContainer = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.xl};
  overflow-x: auto;
`;

const StyledTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const TableHeader = styled.th`
  padding: ${({ theme }) => theme.spacing.md};
  text-align: left;
  font-weight: ${({ theme }) => theme.fontWeights.semiBold};
  color: ${({ theme }) => theme.colors.greenDark};
  border-bottom: 2px solid ${({ theme }) => theme.colors.greenMedium};
`;

const TableCell = styled.td`
  padding: ${({ theme }) => theme.spacing.md};
  border-bottom: 1px solid ${({ theme }) => theme.colors.gray.medium};
`;

const TableRow = styled.tr`
  &:last-child td {
    border-bottom: none;
  }
`;

const StyledLink = styled.a`
  color: ${({ theme }) => theme.colors.greenDark};
  text-decoration: none;
  font-weight: ${({ theme }) => theme.fontWeights.bold};

  &:hover {
    text-decoration: underline;
  }
`;

const Pricing: React.FC = () => (
  <PageContainer>
    <Heading1>Pricing</Heading1>
    <Paragraph>
      Fees for Ontario Court transcripts are set out in <StyledLink href="https://www.ontario.ca/laws/regulation/140094" target="_blank" rel="noopener noreferrer">Ontario Regulation 94/14.</StyledLink>
    </Paragraph>
    <Section>
      <Heading2>Price Per Page</Heading2>
      <TableContainer>
        <StyledTable>
          <thead>
            <tr>
              <TableHeader>Turnaround Time</TableHeader>
              <TableHeader>Rate per Page*<br />(Electronic Format)</TableHeader>
              <TableHeader>Rate per Page*<br />(Paper Format)</TableHeader>
              <TableHeader>Minimum Charge*</TableHeader>
            </tr>
          </thead>
          <tbody>
            <TableRow>
              <TableCell>Within 24 hours</TableCell>
              <TableCell>$11.75</TableCell>
              <TableCell>$12.55</TableCell>
              <TableCell>$25</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Within 5 business days</TableCell>
              <TableCell>$8.80</TableCell>
              <TableCell>$9.60</TableCell>
              <TableCell>$25</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Standard (more than 5 business days)</TableCell>
              <TableCell>$6.30</TableCell>
              <TableCell>$7.10</TableCell>
              <TableCell>$25</TableCell>
            </TableRow>
          </tbody>
          <tfoot>
            <TableRow>
              <TableCell colSpan={4}>
                <SmallText><i>* HST not included</i></SmallText>
              </TableCell>
            </TableRow>
          </tfoot>
        </StyledTable>
      </TableContainer>

      <Paragraph>
        For all other transcription matters, please contact us for pricing. We would be happy to discuss this is more detail.
      </Paragraph>
    </Section>
  </PageContainer>
);

export default Pricing;
