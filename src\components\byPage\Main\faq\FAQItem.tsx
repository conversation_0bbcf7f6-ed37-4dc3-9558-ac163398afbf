import React from 'react';
import {
  FAQItemButton,
  FAQItemIcon,
  FAQItemText,
  FAQItemArrow,
  FAQItemWrapper,
  MobileAccordionContent
} from './FAQ.styles';
import FAQContent from './FAQContent';
import { PlayArrow } from '@mui/icons-material';
import { KeyboardArrowDown as DownArrow } from '@mui/icons-material';

interface FAQItemProps {
  question: string;
  isActive: boolean;
  onClick: () => void;
  content?: {
    title: string;
    description?: string;
    services?: string[];
    contactText?: string;
  };
  isMobileExpanded?: boolean;
}

const FAQItem: React.FC<FAQItemProps> = ({
  question,
  isActive,
  onClick,
  content,
  isMobileExpanded = false
}) => {
  return (
    <FAQItemWrapper>
      <FAQItemButton isActive={isActive} onClick={onClick}>
        <FAQItemIcon isActive={isActive} />
        <FAQItemText>{question}</FAQItemText>
        <FAQItemArrow>
          {isMobileExpanded ? <DownArrow />: <PlayArrow />}
        </FAQItemArrow>
      </FAQItemButton>

      {/* Mobile accordion content */}
      {content && (
        <MobileAccordionContent isOpen={isMobileExpanded}>
          <FAQContent content={content} useContainer={false} />
        </MobileAccordionContent>
      )}
    </FAQItemWrapper>
  );
};

export default FAQItem;
