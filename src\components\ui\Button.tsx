import styled, { css } from 'styled-components';

// Button variants
export type ButtonVariant = 'primary' | 'secondary' | 'outline';

export interface ButtonProps {
  variant?: ButtonVariant;
  fullWidth?: boolean;
}

export const Button = styled.button.withConfig({
  shouldForwardProp: (prop) => prop !== 'fullWidth',
})<ButtonProps>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: ${({ theme }) => theme.borderRadius.small};
  font-size: ${({ theme }) => theme.fontSizes.medium};
  font-weight: ${({ theme }) => theme.fontWeights.semiBold};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.default};
  box-shadow: ${({ theme }) => theme.shadows.button};
  width: ${({ fullWidth }) => (fullWidth ? '100%' : 'auto')};

  ${({ variant = 'primary', theme }) => {
    switch (variant) {
      case 'primary':
        return css`
          background: ${theme.colors.greenDark};
          color: ${theme.colors.white};
          border: none;

          &:hover, &:focus {
            background: ${theme.colors.greenMedium};
          }
        `;
      case 'secondary':
        return css`
          background: ${theme.colors.white};
          color: ${theme.colors.greenDark};
          border: 2px solid ${theme.colors.greenDark};

          &:hover, &:focus {
            background: ${theme.colors.greenMedium};
            color: ${theme.colors.white};
            border-color: ${theme.colors.greenMedium};
          }
        `;
      case 'outline':
        return css`
          background: transparent;
          color: ${theme.colors.greenDark};
          border: 1px solid ${theme.colors.greenDark};

          &:hover, &:focus {
            background: ${theme.colors.greenDark};
            color: ${theme.colors.white};
          }
        `;
      default:
        return '';
    }
  }}
`;

export default Button;
