import React, { useState } from 'react';
import { <PERSON>er<PERSON>ontainer, HeaderInner } from './styles/HeaderContainer.styles';
import { Logo, DesktopNavigation, HamburgerButton, MobileNavigation, Overlay } from './components';

export interface NavigationItem {
  path: string;
  label: string;
  exact?: boolean;
  isButton?: boolean;
}

const Header: React.FC = () => {
  // Define navigation items to be used by both desktop and mobile navigation
  const navigationItems: NavigationItem[] = [
    // { path: '/', label: 'Home', exact: true },
    { path: '/about', label: 'About' },
    { path: '/services', label: 'Services' },
    { path: '/pricing', label: 'Pricing' },
    // { path: '/faqs', label: 'FAQs' },
    // { path: '/contact', label: 'Contact' },
    { path: '/contact', label: 'Contact Us', isButton: true }
  ];
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  // Effect to prevent body scrolling when menu is open
  React.useEffect(() => {
    if (isMenuOpen) {
      // Lock scroll
      document.body.style.overflow = 'hidden';
    } else {
      // Restore scroll
      document.body.style.overflow = '';
    }

    // Cleanup function to ensure scroll is restored
    return () => {
      document.body.style.overflow = '';
    };
  }, [isMenuOpen]);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  // The overlay now handles clicks outside the menu

  return (
    <>
      <HeaderContainer>
        <HeaderInner>
          <Logo />
          <Overlay isVisible={isMenuOpen} onClick={closeMenu} />
          <DesktopNavigation navigationItems={navigationItems} />
          <HamburgerButton isOpen={isMenuOpen} onClick={toggleMenu} />
          <MobileNavigation navigationItems={navigationItems} isOpen={isMenuOpen} onClose={closeMenu} />
        </HeaderInner>
      </HeaderContainer>
    </>
  );
};

export default Header;
