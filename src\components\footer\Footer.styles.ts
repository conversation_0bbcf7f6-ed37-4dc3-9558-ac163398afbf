import styled from 'styled-components';

export const FooterContainer = styled.footer`
  background: ${({ theme }) => theme.colors.greenDark};
  color: ${({ theme }) => theme.colors.white};
  margin-top: auto;
  padding: ${({ theme }) => theme.spacing.lg} 1rem ${({ theme }) => theme.spacing.lg};
  position: relative;

`;

export const FooterGradient = styled.div`
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 25px;
    background: ${({ theme }) => `linear-gradient(to right, ${theme.colors.greenDark}, ${theme.colors.greenMedium}, ${theme.colors.greenLight})`};

`;

export const FooterContent = styled.div`
  max-width: ${({ theme }) => theme.maxWidth};
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.md};
  padding-top: 25px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: ${({ theme }) => theme.spacing.xl};

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    grid-template-columns: repeat(2, 1fr);
    gap: ${({ theme }) => theme.spacing.md};
    padding-top: 25px;
  }
`;

export const FooterColumnLeft = styled.div`
  display: flex;
  flex-direction: column;
`;

export const FooterColumnRight = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-end;

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    /* Maintain right alignment in mobile */
    align-items: flex-end;
    font-size: ${({ theme }) => theme.fontSizes.small};
  }
`;

export const FooterHeading = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.medium};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  margin-bottom: ${({ theme }) => theme.spacing.md};

`;

export const FooterLink = styled.a`
  color: ${({ theme }) => theme.colors.white};
  text-decoration: none;
  font-size: ${({ theme }) => theme.fontSizes.small};
  transition: color ${({ theme }) => theme.transitions.default};

  &:hover {
    color: ${({ theme }) => theme.colors.gray.light};
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    font-size: ${({ theme }) => theme.fontSizes.small};
    white-space: nowrap;
  }
`;

export const ContactItem = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.sm};
  font-size: ${({ theme }) => theme.fontSizes.small};

  img {
    margin-left: 10px;
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    font-size: ${({ theme }) => theme.fontSizes.small};
    white-space: nowrap;
  }
`;

export const CopyrightContainer = styled.div`
  text-align: center;
  padding-top: ${({ theme }) => theme.spacing.md};
  padding-bottom: ${({ theme }) => theme.spacing.xs};
  margin-top: ${({ theme }) => theme.spacing.lg};
`;


export const FooterImage = styled.img`
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    display: none;
  }
`
