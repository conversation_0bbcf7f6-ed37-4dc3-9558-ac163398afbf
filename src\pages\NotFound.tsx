import React from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { Heading1, Paragraph } from '../components/typography';
import { Button } from '../components/ui';

const NotFoundContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: ${({ theme }) => theme.spacing.xxl} ${({ theme }) => theme.spacing.xl};
`;

const ErrorCode = styled.div`
  font-size: 8rem;
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.greenLight};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  line-height: 1;
`;

const ButtonContainer = styled.div`
  margin-top: ${({ theme }) => theme.spacing.xl};
`;

const NotFound: React.FC = () => {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate('/');
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <NotFoundContainer>
      <ErrorCode>404</ErrorCode>
      <Heading1>Page Not Found</Heading1>
      <Paragraph>
        We're sorry, the page you are looking for doesn't exist or has been moved.
      </Paragraph>
      <ButtonContainer>
          <Button variant="secondary" onClick={handleGoBack}>
            Go Back
          </Button>
          <Button variant="primary" onClick={handleGoHome}>
            Go Home
          </Button>
      </ButtonContainer>
    </NotFoundContainer>
  );
};

export default NotFound;
