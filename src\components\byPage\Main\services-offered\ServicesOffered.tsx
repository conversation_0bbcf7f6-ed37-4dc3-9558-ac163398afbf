import React from 'react';
import { Heading2, ListItem } from '../../../typography';
import styled from 'styled-components';
import { Section } from '../../../layout/components';

export const ServicesOfferedSection = styled(Section)`
  padding: ${({ theme }) => theme.spacing.xxl} 0rem;
  background-color: ${({ theme }) => theme.colors.gray.light};
  margin: ${({ theme }) => theme.spacing.xl} 0;
  border-radius: ${({ theme }) => theme.borderRadius.medium};

   @media (max-width: ${({ theme }) => theme.breakpoints.laptop}) {
    padding: ${({ theme }) => theme.spacing.xl} 1rem;
  }
`;

export const ServicesOfferedContainer = styled.div`
  display: flex;
  align-items: center;
  
  @media (min-width: ${({ theme }) => theme.breakpoints.laptop}) {
    padding: 0 5rem;
  }
  @media (max-width: ${({ theme }) => theme.breakpoints.laptop}) {
    flex-direction: column;
    text-align: center;
  }
`;

export const ImageContainer = styled.div`
  flex: 1;
  display: flex;
  max-width: 50%;
  justify-content: center;
    
  @media (min-width: ${({ theme }) => theme.breakpoints.laptop}) {
    padding: 0 1.5rem;
  }
  @media (max-width: ${({ theme }) => theme.breakpoints.laptop}) {
    max-width: 100%;
    margin-bottom: ${({ theme }) => theme.spacing.xl};
  }
`;

export const ContentContainer = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 500px;
  padding-right: ${({ theme }) => theme.spacing.sm};
`;

export const ServiceImage = styled.img`
  display: flex;
  width: 350px;
  height: auto;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
  border-radius: ${({ theme }) => theme.borderRadius.large};
  box-shadow: ${({ theme }) => theme.shadows.card};
`;

export const ServicesList = styled.ul`
  list-style-position: inside;
  margin: ${({ theme }) => theme.spacing.md} 0;
  padding-left: ${({ theme }) => theme.spacing.md};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.laptop}) {
    text-align: left;
  }
`;

const ServicesOffered: React.FC = () => {

  return (
    <ServicesOfferedSection>
      <ServicesOfferedContainer>
        <ImageContainer>
          <ServiceImage
            src="/images/Library.jpeg"
            alt="Legal transcription services"
          />
        </ImageContainer>
        <ContentContainer>
          <Heading2>Legal Transcription Services Offered</Heading2>
          <ServicesList>
            <ListItem>Certified transcripts from all Ontario Court proceedings</ListItem>
            <ListItem>Police Interviews, 911 calls, recorded voicemail messages</ListItem>
            <ListItem>Transcription of discoveries, statements made by involved parties, and other legal pleadings</ListItem>
          </ServicesList>
        </ContentContainer>
      </ServicesOfferedContainer>
    </ServicesOfferedSection>
  );
};

export default ServicesOffered;
