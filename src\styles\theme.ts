// Theme configuration for styled-components
const theme = {
  colors: {
    greenLight: '#66B538',
    greenMedium: '#147838',
    greenDark: '#134713',
    background: '#FFFFFF',
    text: '#134713',
    white: '#FFFFFF',
    black: '#000000',
    gray: {
      light: '#F5F5F5',
      medium: '#E0E0E0',
      dark: '#757575'
    }
  },
  opaqueColors: {
    white: {
      90: 'rgba(255, 255, 255, 0.9)',
      80: 'rgba(255, 255, 255, 0.8)',
      70: 'rgba(255, 255, 255, 0.7)',
      60: 'rgba(255, 255, 255, 0.6)',
      50: 'rgba(255, 255, 255, 0.5)',
      40: 'rgba(255, 255, 255, 0.4)',
      30: 'rgba(255, 255, 255, 0.3)',
      20: 'rgba(255, 255, 255, 0.2)',
      10: 'rgba(255, 255, 255, 0.1)'
    },
    grayMedium: {
      90: 'rgba(224, 224, 224, 0.9)',
      80: 'rgba(224, 224, 224, 0.8)',
      70: 'rgba(224, 224, 224, 0.7)',
      60: 'rgba(224, 224, 224, 0.6)', 
      50: 'rgba(224, 224, 224, 0.5)',
      40: 'rgba(224, 224, 224, 0.4)',
      30: 'rgba(224, 224, 224, 0.3)',
      20: 'rgba(224, 224, 224, 0.2)',
      10: 'rgba(224, 224, 224, 0.1)'
    },
    greenDark: {
      90: 'rgba(117,117,117, 0.9)',
      80: 'rgba(117,117,117, 0.8)',
      70: 'rgba(117,117,117, 0.7)',
      60: 'rgba(117,117,117, 0.6)', 
      50: 'rgba(117,117,117, 0.5)',
      40: 'rgba(117,117,117, 0.4)',
      30: 'rgba(117,117,117, 0.3)',
      20: 'rgba(117,117,117, 0.2)',
      10: 'rgba(117,117,117, 0.1)'
    },
    greenLight: {
      90: 'rgba(98, 176, 54, 0.9)',
      80: 'rgba(98, 176, 54, 0.8)',
      70: 'rgba(98, 176, 54, 0.7)',
      60: 'rgba(98, 176, 54, 0.6)', 
      50: 'rgba(98, 176, 54, 0.5)',
      40: 'rgba(98, 176, 54, 0.4)',
      30: 'rgba(98, 176, 54, 0.3)',
      20: 'rgba(98, 176, 54, 0.2)',
      10: 'rgba(98, 176, 54, 0.1)',
      5: 'rgba(98, 176, 54, 0.05)'
    }
  },
  fonts: {
    letterSpacing: {
      tight: '-0.05em',
      normal: '0em',
      wideHalf: '0.025em',
      wide: '0.05em'
    },
    primary: "Albert Sans"
  },
  fontSizes: {
    small: '0.875rem',
    medium: '1rem',
    large: '1.25rem',
    xlarge: '1.5rem',
    xxlarge: '2rem',
    hero: '2.8rem'
  },
  fontWeights: {
    regular: 400,
    medium: 500,
    semiBold: 600,
    bold: 700
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    xxl: '3rem'
  },
  borderRadius: {
    small: '4px',
    medium: '8px',
    large: '16px',
    xlarge: '1.5rem'
  },
  shadows: {
    small: '0 2px 8px rgba(0, 0, 0, 0.04)',
    medium: '0 4px 12px rgba(0, 0, 0, 0.06)',
    large: '0 8px 24px rgba(0, 0, 0, 0.08)',
    button: '0 2px 8px 0 rgba(20, 120, 56, 0.10)',
    card: '0 4px 32px 0 rgba(20, 120, 56, 0.10)'
  },
  breakpoints: {
    at400: '400px',
    mobile: '576px',
    tablet: '768px',
    laptop: '992px',
    desktop: '1200px'
  },
  transitions: {
    default: '0.2s ease',
    slow: '0.4s ease'
  },
  maxWidth: '1440px'
};

export default theme;

// Type definitions for the theme
export type ThemeType = typeof theme;
