import styled, { css } from 'styled-components';

export const MobileMenuToggle = styled.button`
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: ${({ theme }) => theme.spacing.sm};
  margin-left: auto;
  position: relative;

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    display: block;
    width: 30px;
    height: 21px;
  }
`;

interface HamburgerBarProps {
  isOpen?: boolean;
  position?: 'top' | 'middle' | 'bottom';
}

export const HamburgerBar = styled.span.withConfig({
  shouldForwardProp: (prop) => !['isOpen', 'position'].includes(prop as string),
})<HamburgerBarProps>`
  &::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
  }
  display: block;
  width: 100%;
  height: 3px;
  background-color: ${({ theme }) => theme.colors.greenDark};
  border-radius: 3px;
  transition: all ${({ theme }) => theme.transitions.default};
  position: absolute;

  ${({ position }) => {
    if (position === 'top') {
      return css`
        top: 0;
      `;
    }
    if (position === 'middle') {
      return css`
        top: calc(50% - 1.5px);
      `;
    }
    if (position === 'bottom') {
      return css`
        bottom: 0;
      `;
    }
    return '';
  }}

  ${({ isOpen, position }) => {
    if (isOpen && position === 'top') {
      return css`
        top: 50%;
        transform: translateY(-50%) rotate(45deg);
      `;
    }
    if (isOpen && position === 'middle') {
      return css`
        opacity: 0;
      `;
    }
    if (isOpen && position === 'bottom') {
      return css`
        bottom: auto;
        top: 50%;
        transform: translateY(-50%) rotate(-45deg);
      `;
    }
    return '';
  }}
`;
