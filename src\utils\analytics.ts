import { analytics } from '../firebase';
import { logEvent, setAnalyticsCollectionEnabled } from 'firebase/analytics';

// Check if we're in development mode
const isDevelopment = import.meta.env.DEV;

// Log to console in development mode
const devLog = (message: string, ...args: any[]) => {
  if (isDevelopment) {
    console.log(`[DEV ONLY] ${message}`, ...args);
  }
};

/**
 * Enables or disables analytics collection
 * @param enabled Whether analytics collection should be enabled
 */
export const setAnalyticsEnabled = (enabled: boolean) => {
  // In development mode, don't actually enable analytics
  if (isDevelopment) {
    devLog(`Analytics would be ${enabled ? 'enabled' : 'disabled'} in production`);
    return;
  }

  if (analytics) {
    setAnalyticsCollectionEnabled(analytics, enabled);
  }
};

/**
 * Logs a page view event in Google Analytics
 * @param pagePath The path of the page (e.g., '/about')
 * @param pageTitle The title of the page (e.g., 'About Us')
 */
export const logPageView = (pagePath: string, pageTitle: string) => {
  // In development mode, just log to console
  if (isDevelopment) {
    devLog(`Page view: ${pagePath} - ${pageTitle}`);
    return;
  }

  if (analytics) {
    logEvent(analytics, 'page_view', {
      page_path: pagePath,
      page_title: pageTitle,
      page_location: window.location.href
    });
  }
};

/**
 * Logs a custom event in Google Analytics
 * @param eventName The name of the event
 * @param eventParams Additional parameters for the event
 */
export const logCustomEvent = (eventName: string, eventParams?: Record<string, any>) => {
  // In development mode, just log to console
  if (isDevelopment) {
    devLog(`Custom event: ${eventName}`, eventParams);
    return;
  }

  if (analytics) {
    logEvent(analytics, eventName, eventParams);
  }
};

/**
 * Logs a button click event
 * @param buttonName The name or identifier of the button
 * @param buttonLocation The location or context of the button (e.g., 'hero', 'footer')
 */
export const logButtonClick = (buttonName: string, buttonLocation: string) => {
  logCustomEvent('button_click', {
    button_name: buttonName,
    button_location: buttonLocation
  });
};

/**
 * Logs a form submission event
 * @param formName The name or identifier of the form
 * @param formSuccess Whether the form submission was successful
 */
export const logFormSubmit = (formName: string, formSuccess: boolean) => {
  logCustomEvent('form_submit', {
    form_name: formName,
    form_success: formSuccess
  });
};

/**
 * Logs a service selection event
 * @param serviceName The name of the service selected
 */
export const logServiceSelection = (serviceName: string) => {
  logCustomEvent('service_selection', {
    service_name: serviceName
  });
};

/**
 * Logs an outbound link click
 * @param linkUrl The URL of the outbound link
 * @param linkText The text content of the link
 */
export const logOutboundLink = (linkUrl: string, linkText: string) => {
  logCustomEvent('outbound_link', {
    link_url: linkUrl,
    link_text: linkText
  });
};

/**
 * Checks if analytics is currently running in development mode
 * @returns True if in development mode, false otherwise
 */
export const isAnalyticsInDevMode = (): boolean => {
  return isDevelopment;
};
